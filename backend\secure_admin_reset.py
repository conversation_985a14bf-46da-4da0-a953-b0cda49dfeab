#!/usr/bin/env python3
"""
Secure Admin Password Reset Script
This script forces a secure password reset for the admin user to address security vulnerabilities.
"""

import os
import sys
import secrets
import string
from flask import Flask
from models import db, User

def generate_secure_password(length=16):
    """Generate a cryptographically secure password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password

def reset_admin_password():
    """Reset admin password to a secure random password"""

    # Create Flask app context
    app = Flask(__name__)

    # Configure database
    if os.path.exists('/database'):
        db_path = os.path.join('/database', 'tools.db')
    else:
        db_path = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'database', 'tools.db'))

    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    db.init_app(app)

    with app.app_context():
        # Find admin user
        admin = User.query.filter_by(employee_number='ADMIN001').first()

        if not admin:
            print("❌ Admin user ADMIN001 not found!")
            return False

        # Check if using weak password
        weak_passwords = ['admin123', 'password', 'admin', '123456', 'password123']
        is_weak = False

        for weak_pwd in weak_passwords:
            if admin.check_password(weak_pwd):
                print(f"🚨 SECURITY ALERT: Admin is using weak password '{weak_pwd}'")
                is_weak = True
                break

        if not is_weak:
            print("✅ Admin password appears to be secure already")
            return True

        # Generate new secure password
        new_password = generate_secure_password(20)

        # Update admin password
        admin.set_password(new_password)
        admin.force_password_change = True  # Force change on next login

        try:
            db.session.commit()
            print("✅ Admin password has been reset successfully!")
            print("=" * 60)
            print("🔐 NEW ADMIN CREDENTIALS")
            print(f"Username: ADMIN001")
            print(f"Password: {new_password}")
            print("=" * 60)
            print("⚠️  IMPORTANT SECURITY NOTES:")
            print("1. Save this password securely immediately")
            print("2. Admin will be forced to change password on next login")
            print("3. Delete this output from terminal history")
            print("4. Consider setting INITIAL_ADMIN_PASSWORD environment variable")
            print("=" * 60)
            return True

        except Exception as e:
            print(f"❌ Error updating admin password: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🔒 SupplyLine MRO Suite - Secure Admin Reset")
    print("=" * 50)

    # Confirm action
    confirm = input("This will reset the admin password. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        sys.exit(0)

    success = reset_admin_password()

    if success:
        print("\n✅ Security fix completed successfully!")
        print("🚀 You can now log in with the new secure credentials.")
    else:
        print("\n❌ Security fix failed!")
        sys.exit(1)